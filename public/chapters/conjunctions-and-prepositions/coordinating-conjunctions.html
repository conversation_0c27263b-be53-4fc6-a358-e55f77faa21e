<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>并列连词</title>
    <script src="/static/tailwindcss-3.4.17.js"></script>
    <style>
        .keyword {
            color: #3d74ed;
            font-weight: bold;
        }
        .card {
            min-height: 120px;
        }
    </style>
</head>
<body class="bg-white">
<div class="p-6">
        
        <!-- 并列连词概述 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">并列连词基础</h2>
            <p class="text-gray-700 mb-4 leading-7">并列连词（Coordinating Conjunctions）是用来连接两个语法地位相等的词、短语或句子的连词。最常见的并列连词可以用缩写词"FANBOYS"来记忆：<span class="keyword">F</span>or, <span class="keyword">A</span>nd, <span class="keyword">N</span>or, <span class="keyword">B</span>ut, <span class="keyword">O</span>r, <span class="keyword">Y</span>et, <span class="keyword">S</span>o。</p>
            <p class="text-gray-700 leading-7">这些连词在美式英语中使用频率极高，掌握它们的用法对于提高英语表达的流畅性和准确性至关重要。</p>
        </section>

        <!-- AND - 表示并列和递增 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">And - 表示并列和递增</h3>
            <p class="text-gray-700 mb-4 leading-7"><span class="keyword">And</span> 是最基础的并列连词，用于连接相似或相关的内容，表示"和"、"与"、"而且"的含义。</p>
            
            <div class="mb-4">
                <h4 class="text-lg font-medium mb-3 text-gray-700">基本用法示例</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">连接名词</div>
                        <div class="keyword text-lg mb-1">I like coffee and tea.</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪ laɪk ˈkɔːfi ænd tiː/</div>
                        <div class="text-gray-700">我喜欢咖啡和茶。</div>
                    </div>
                    <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">连接形容词</div>
                        <div class="keyword text-lg mb-1">She is smart and beautiful.</div>
                        <div class="text-sm text-gray-600 mb-1">/ʃi ɪz smɑːrt ænd ˈbjuːtɪfəl/</div>
                        <div class="text-gray-700">她聪明又美丽。</div>
                    </div>
                    <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">连接动词</div>
                        <div class="keyword text-lg mb-1">He runs and swims every day.</div>
                        <div class="text-sm text-gray-600 mb-1">/hi rʌnz ænd swɪmz ˈevri deɪ/</div>
                        <div class="text-gray-700">他每天跑步和游泳。</div>
                    </div>
                    <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">连接句子</div>
                        <div class="keyword text-lg mb-1">I finished my work, and I went home.</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪ ˈfɪnɪʃt maɪ wɜːrk ænd aɪ went hoʊm/</div>
                        <div class="text-gray-700">我完成了工作，然后回家了。</div>
                    </div>
                </div>
            </div>

            <div class="mb-4">
                <h4 class="text-lg font-medium mb-3 text-gray-700">特殊用法</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">表示结果</div>
                        <div class="keyword text-lg mb-1">Study hard, and you will succeed.</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈstʌdi hɑːrd ænd ju wɪl səkˈsiːd/</div>
                        <div class="text-gray-700">努力学习，你就会成功。</div>
                    </div>
                    <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">表示强调</div>
                        <div class="keyword text-lg mb-1">Again and again she called.</div>
                        <div class="text-sm text-gray-600 mb-1">/əˈgen ænd əˈgen ʃi kɔːld/</div>
                        <div class="text-gray-700">她一次又一次地打电话。</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- BUT - 表示转折 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">But - 表示转折对比</h3>
            <p class="text-gray-700 mb-4 leading-7"><span class="keyword">But</span> 用于表示转折、对比或相反的情况，相当于中文的"但是"、"可是"、"然而"。</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">基本转折</div>
                    <div class="keyword text-lg mb-1">I like pizza, but I'm on a diet.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ laɪk ˈpiːtsə bʌt aɪm ɑːn ə ˈdaɪət/</div>
                    <div class="text-gray-700">我喜欢披萨，但我在节食。</div>
                </div>
                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">性格对比</div>
                    <div class="keyword text-lg mb-1">She is young but wise.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi ɪz jʌŋ bʌt waɪz/</div>
                    <div class="text-gray-700">她年轻但很聪慧。</div>
                </div>
                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">能力对比</div>
                    <div class="keyword text-lg mb-1">He can read, but he can't write.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi kæn riːd bʌt hi kænt raɪt/</div>
                    <div class="text-gray-700">他会读，但不会写。</div>
                </div>
                <div class="card bg-gray-50 border border-gray-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">情况转折</div>
                    <div class="keyword text-lg mb-1">It's expensive, but it's worth it.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪts ɪkˈspensɪv bʌt ɪts wɜːrθ ɪt/</div>
                    <div class="text-gray-700">很贵，但物有所值。</div>
                </div>
            </div>
        </section>

        <!-- OR - 表示选择 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">Or - 表示选择</h3>
            <p class="text-gray-700 mb-4 leading-7"><span class="keyword">Or</span> 用于表示选择关系，相当于中文的"或者"、"还是"。可以表示两个或多个选项中的任选其一。</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">二选一</div>
                    <div class="keyword text-lg mb-1">Do you want tea or coffee?</div>
                    <div class="text-sm text-gray-600 mb-1">/du ju wɑːnt tiː ɔːr ˈkɔːfi/</div>
                    <div class="text-gray-700">你想要茶还是咖啡？</div>
                </div>
                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">多个选择</div>
                    <div class="keyword text-lg mb-1">We can walk, drive, or take the bus.</div>
                    <div class="text-sm text-gray-600 mb-1">/wi kæn wɔːk draɪv ɔːr teɪk ðə bʌs/</div>
                    <div class="text-gray-700">我们可以走路、开车或坐公交。</div>
                </div>
                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">否则/要不然</div>
                    <div class="keyword text-lg mb-1">Hurry up, or you'll be late.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈhɜːri ʌp ɔːr jul bi leɪt/</div>
                    <div class="text-gray-700">快点，否则你会迟到的。</div>
                </div>
                <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">补充说明</div>
                    <div class="keyword text-lg mb-1">He is a doctor or something like that.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi ɪz ə ˈdɑːktər ɔːr ˈsʌmθɪŋ laɪk ðæt/</div>
                    <div class="text-gray-700">他是医生还是类似的职业。</div>
                </div>
            </div>
        </section>

        <!-- SO - 表示因果关系 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">So - 表示因果关系</h3>
            <p class="text-gray-700 mb-4 leading-7"><span class="keyword">So</span> 用于表示结果或因果关系，相当于中文的"所以"、"因此"、"那么"。</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">因果关系</div>
                    <div class="keyword text-lg mb-1">It was raining, so I stayed home.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪt wʌz ˈreɪnɪŋ soʊ aɪ steɪd hoʊm/</div>
                    <div class="text-gray-700">下雨了，所以我待在家里。</div>
                </div>
                <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">结果导向</div>
                    <div class="keyword text-lg mb-1">I was tired, so I went to bed early.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ wʌz ˈtaɪərd soʊ aɪ went tu bed ˈɜːrli/</div>
                    <div class="text-gray-700">我累了，所以很早就睡了。</div>
                </div>
                <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">逻辑推理</div>
                    <div class="keyword text-lg mb-1">She studied hard, so she passed the exam.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi ˈstʌdid hɑːrd soʊ ʃi pæst ði ɪgˈzæm/</div>
                    <div class="text-gray-700">她努力学习，所以通过了考试。</div>
                </div>
                <div class="card bg-stone-50 border border-stone-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">决定行动</div>
                    <div class="keyword text-lg mb-1">The store was closed, so we went elsewhere.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə stɔːr wʌz kloʊzd soʊ wi went ˈelsˌwer/</div>
                    <div class="text-gray-700">商店关门了，所以我们去了别处。</div>
                </div>
            </div>
        </section>

        <!-- YET - 表示转折 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">Yet - 表示意外转折</h3>
            <p class="text-gray-700 mb-4 leading-7"><span class="keyword">Yet</span> 表示意外的转折或对比，比but更正式，常用于书面语，相当于中文的"然而"、"可是"、"不过"。</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div class="card bg-fuchsia-50 border border-fuchsia-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">意外转折</div>
                    <div class="keyword text-lg mb-1">He is old, yet he is very active.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi ɪz oʊld jet hi ɪz ˈveri ˈæktɪv/</div>
                    <div class="text-gray-700">他年纪大了，然而却很活跃。</div>
                </div>
                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">能力反差</div>
                    <div class="keyword text-lg mb-1">She is small, yet she is strong.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi ɪz smɔːl jet ʃi ɪz strɔːŋ/</div>
                    <div class="text-gray-700">她个子小，但很强壮。</div>
                </div>
                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">情况相反</div>
                    <div class="keyword text-lg mb-1">It's simple, yet effective.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪts ˈsɪmpəl jet ɪˈfektɪv/</div>
                    <div class="text-gray-700">这很简单，却很有效。</div>
                </div>
                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">期望相反</div>
                    <div class="keyword text-lg mb-1">The task was difficult, yet we completed it.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə tæsk wʌz ˈdɪfɪkəlt jet wi kəmˈpliːtɪd ɪt/</div>
                    <div class="text-gray-700">任务很困难，但我们完成了。</div>
                </div>
            </div>
        </section>

        <!-- FOR - 表示原因 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">For - 表示原因解释</h3>
            <p class="text-gray-700 mb-4 leading-7"><span class="keyword">For</span> 用于表示原因或理由，较为正式，多用于书面语，相当于中文的"因为"、"由于"。</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">解释原因</div>
                    <div class="keyword text-lg mb-1">I must leave now, for it's getting late.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ mʌst liːv naʊ fɔːr ɪts ˈgetɪŋ leɪt/</div>
                    <div class="text-gray-700">我现在必须走了，因为天晚了。</div>
                </div>
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">推理依据</div>
                    <div class="keyword text-lg mb-1">He must be home, for his car is here.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi mʌst bi hoʊm fɔːr hɪz kɑːr ɪz hɪr/</div>
                    <div class="text-gray-700">他一定在家，因为他的车在这里。</div>
                </div>
                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">行为理由</div>
                    <div class="keyword text-lg mb-1">She smiled, for she was happy.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi smaɪld fɔːr ʃi wʌz ˈhæpi/</div>
                    <div class="text-gray-700">她笑了，因为她很开心。</div>
                </div>
                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">状态说明</div>
                    <div class="keyword text-lg mb-1">The ground is wet, for it rained last night.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə graʊnd ɪz wet fɔːr ɪt reɪnd læst naɪt/</div>
                    <div class="text-gray-700">地面是湿的，因为昨晚下雨了。</div>
                </div>
            </div>
        </section>

        <!-- NOR - 表示递进否定 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">Nor - 表示递进否定</h3>
            <p class="text-gray-700 mb-4 leading-7"><span class="keyword">Nor</span> 用于连接两个否定的概念，表示"也不"、"又不"，通常与neither搭配使用，或在否定句后继续否定。</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">Neither...nor结构</div>
                    <div class="keyword text-lg mb-1">Neither he nor she came to the party.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈniːðər hi nɔːr ʃi keɪm tu ðə ˈpɑːrti/</div>
                    <div class="text-gray-700">他和她都没来参加聚会。</div>
                </div>
                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">递进否定</div>
                    <div class="keyword text-lg mb-1">I don't like coffee, nor do I like tea.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ doʊnt laɪk ˈkɔːfi nɔːr du aɪ laɪk tiː/</div>
                    <div class="text-gray-700">我不喜欢咖啡，也不喜欢茶。</div>
                </div>
                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">能力否定</div>
                    <div class="keyword text-lg mb-1">He can't read, nor can he write.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi kænt riːd nɔːr kæn hi raɪt/</div>
                    <div class="text-gray-700">他不能读，也不能写。</div>
                </div>
                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">情况否定</div>
                    <div class="keyword text-lg mb-1">It's not hot, nor is it cold.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪts nɑːt hɑːt nɔːr ɪz ɪt koʊld/</div>
                    <div class="text-gray-700">天气不热，也不冷。</div>
                </div>
            </div>
        </section>

        <!-- 实用表达和习惯用法 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">常用组合表达</h3>
            <p class="text-gray-700 mb-4 leading-7">并列连词在实际应用中经常与其他词汇组合，形成固定的表达方式。</p>
            
            <div class="mb-4">
                <h4 class="text-lg font-medium mb-3 text-gray-700">And的常用组合</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="card bg-slate-50 border border-slate-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">so on and so forth</div>
                        <div class="keyword text-lg mb-1">Books, papers, and so on.</div>
                        <div class="text-sm text-gray-600 mb-1">/bʊks ˈpeɪpərz ænd soʊ ɑːn/</div>
                        <div class="text-gray-700">书籍、论文等等。</div>
                    </div>
                    <div class="card bg-neutral-50 border border-neutral-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">now and then</div>
                        <div class="keyword text-lg mb-1">I visit my parents now and then.</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪ ˈvɪzɪt maɪ ˈperənts naʊ ænd ðen/</div>
                        <div class="text-gray-700">我时不时去看望父母。</div>
                    </div>
                    <div class="card bg-zinc-50 border border-zinc-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">more and more</div>
                        <div class="keyword text-lg mb-1">More and more people use smartphones.</div>
                        <div class="text-sm text-gray-600 mb-1">/mɔːr ænd mɔːr ˈpiːpəl juːz ˈsmɑːrtˌfoʊnz/</div>
                        <div class="text-gray-700">越来越多的人使用智能手机。</div>
                    </div>
                </div>
            </div>

            <div class="mb-4">
                <h4 class="text-lg font-medium mb-3 text-gray-700">Or的常用组合</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">or so</div>
                        <div class="keyword text-lg mb-1">It takes an hour or so.</div>
                        <div class="text-sm text-gray-600 mb-1">/ɪt teɪks æn ˈaʊər ɔːr soʊ/</div>
                        <div class="text-gray-700">大约需要一个小时。</div>
                    </div>
                    <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">or else</div>
                        <div class="keyword text-lg mb-1">Study hard, or else you'll fail.</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈstʌdi hɑːrd ɔːr els jul feɪl/</div>
                        <div class="text-gray-700">努力学习，否则你会失败。</div>
                    </div>
                    <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">either...or</div>
                        <div class="keyword text-lg mb-1">Either you or I will go.</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈiːðər ju ɔːr aɪ wɪl goʊ/</div>
                        <div class="text-gray-700">要么你去，要么我去。</div>
                    </div>
                </div>
            </div>

            <div class="mb-4">
                <h4 class="text-lg font-medium mb-3 text-gray-700">But的常用组合</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">but also</div>
                        <div class="keyword text-lg mb-1">Not only smart but also kind.</div>
                        <div class="text-sm text-gray-600 mb-1">/nɑːt ˈoʊnli smɑːrt bʌt ˈɔːlsoʊ kaɪnd/</div>
                        <div class="text-gray-700">不仅聪明而且善良。</div>
                    </div>
                    <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">but then</div>
                        <div class="keyword text-lg mb-1">I wanted to go, but then it rained.</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪ ˈwɑːntɪd tu goʊ bʌt ðen ɪt reɪnd/</div>
                        <div class="text-gray-700">我想去的，但是下雨了。</div>
                    </div>
                    <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">anything but</div>
                        <div class="keyword text-lg mb-1">He is anything but lazy.</div>
                        <div class="text-sm text-gray-600 mb-1">/hi ɪz ˈeniθɪŋ bʌt ˈleɪzi/</div>
                        <div class="text-gray-700">他一点也不懒。</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 使用注意事项 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">使用注意事项</h3>
            
            <div class="mb-4">
                <h4 class="text-lg font-medium mb-3 text-gray-700">标点符号规则</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">连接独立句子时加逗号</div>
                        <div class="keyword text-lg mb-1">I studied hard, and I passed the test.</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪ ˈstʌdid hɑːrd ænd aɪ pæst ðə test/</div>
                        <div class="text-gray-700">我努力学习，并且通过了考试。</div>
                    </div>
                    <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">连接词组时不加逗号</div>
                        <div class="keyword text-lg mb-1">I like apples and oranges.</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪ laɪk ˈæpəlz ænd ˈɔːrɪndʒɪz/</div>
                        <div class="text-gray-700">我喜欢苹果和橙子。</div>
                    </div>
                </div>
            </div>

            <div class="mb-4">
                <h4 class="text-lg font-medium mb-3 text-gray-700">语序和倒装</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">Nor引起的倒装</div>
                        <div class="keyword text-lg mb-1">I don't smoke, nor do I drink.</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪ doʊnt smoʊk nɔːr du aɪ drɪŋk/</div>
                        <div class="text-gray-700">我不抽烟，也不喝酒。</div>
                    </div>
                    <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">So表结果的倒装</div>
                        <div class="keyword text-lg mb-1">He likes music, so do I.</div>
                        <div class="text-sm text-gray-600 mb-1">/hi laɪks ˈmjuːzɪk soʊ du aɪ/</div>
                        <div class="text-gray-700">他喜欢音乐，我也喜欢。</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 高级用法和复合结构 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">高级用法和复合结构</h3>
            <p class="text-gray-700 mb-4 leading-7">掌握并列连词的高级用法，能够使英语表达更加流畅自然，符合美式英语习惯。</p>
            
            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">多重并列连词组合</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">三重并列</div>
                        <div class="keyword text-lg mb-1">I need to eat, sleep, and work.</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪ niːd tu iːt sliːp ænd wɜːrk/</div>
                        <div class="text-gray-700">我需要吃饭、睡觉和工作。</div>
                    </div>
                    <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">复合转折</div>
                        <div class="keyword text-lg mb-1">She tried hard, but failed, yet never gave up.</div>
                        <div class="text-sm text-gray-600 mb-1">/ʃi traɪd hɑːrd bʌt feɪld jet ˈnevər geɪv ʌp/</div>
                        <div class="text-gray-700">她努力了，但失败了，却从未放弃。</div>
                    </div>
                    <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">因果链条</div>
                        <div class="keyword text-lg mb-1">It rained, so we stayed home, and watched movies.</div>
                        <div class="text-sm text-gray-600 mb-1">/ɪt reɪnd soʊ wi steɪd hoʊm ænd wɑːtʃt ˈmuːviz/</div>
                        <div class="text-gray-700">下雨了，所以我们待在家里看电影。</div>
                    </div>
                    <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">选择递进</div>
                        <div class="keyword text-lg mb-1">Call me, or text me, or just come over.</div>
                        <div class="text-sm text-gray-600 mb-1">/kɔːl mi ɔːr tekst mi ɔːr dʒʌst kʌm ˈoʊvər/</div>
                        <div class="text-gray-700">给我打电话，或发短信，或直接过来。</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">并列连词的省略用法</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">省略重复词汇</div>
                        <div class="keyword text-lg mb-1">I can sing and (I can) dance.</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪ kæn sɪŋ ænd dæns/</div>
                        <div class="text-gray-700">我会唱歌也会跳舞。</div>
                    </div>
                    <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">省略助动词</div>
                        <div class="keyword text-lg mb-1">She was tired but (she was) happy.</div>
                        <div class="text-sm text-gray-600 mb-1">/ʃi wʌz ˈtaɪərd bʌt ˈhæpi/</div>
                        <div class="text-gray-700">她累了但很开心。</div>
                    </div>
                    <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">省略介词短语</div>
                        <div class="keyword text-lg mb-1">He went to the store and (to the) bank.</div>
                        <div class="text-sm text-gray-600 mb-1">/hi went tu ðə stɔːr ænd bæŋk/</div>
                        <div class="text-gray-700">他去了商店和银行。</div>
                    </div>
                    <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">省略主语</div>
                        <div class="keyword text-lg mb-1">I read the book and (I) loved it.</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪ riːd ðə bʊk ænd lʌvd ɪt/</div>
                        <div class="text-gray-700">我读了这本书，很喜欢。</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 不同语境中的应用 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">不同语境中的应用</h3>
            
            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">日常对话中的连词</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">购物场景</div>
                        <div class="keyword text-lg mb-1">I want the red one, but it's too expensive.</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪ wɑːnt ðə red wʌn bʌt ɪts tuː ɪkˈspensɪv/</div>
                        <div class="text-gray-700">我想要红色的，但太贵了。</div>
                    </div>
                    <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">餐厅点餐</div>
                        <div class="keyword text-lg mb-1">I'll have the pasta and a salad.</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪl hæv ðə ˈpɑːstə ænd ə ˈsæləd/</div>
                        <div class="text-gray-700">我要意大利面和沙拉。</div>
                    </div>
                    <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">工作讨论</div>
                        <div class="keyword text-lg mb-1">We can finish today, or work overtime tomorrow.</div>
                        <div class="text-sm text-gray-600 mb-1">/wi kæn ˈfɪnɪʃ təˈdeɪ ɔːr wɜːrk ˈoʊvərˌtaɪm təˈmɑːroʊ/</div>
                        <div class="text-gray-700">我们可以今天完成，或者明天加班。</div>
                    </div>
                    <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">旅行计划</div>
                        <div class="keyword text-lg mb-1">The weather looks bad, so let's stay indoors.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈweðər lʊks bæd soʊ lets steɪ ˈɪnˌdɔːrz/</div>
                        <div class="text-gray-700">天气看起来不好，所以我们待在室内吧。</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">商务和正式场合</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-slate-50 border border-slate-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">会议发言</div>
                        <div class="keyword text-lg mb-1">The project is complex, yet achievable.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈprɑːdʒekt ɪz kəmˈpleks jet əˈtʃiːvəbəl/</div>
                        <div class="text-gray-700">这个项目很复杂，但是可以实现。</div>
                    </div>
                    <div class="card bg-stone-50 border border-stone-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">商务邮件</div>
                        <div class="keyword text-lg mb-1">We must act quickly, for time is limited.</div>
                        <div class="text-sm text-gray-600 mb-1">/wi mʌst ækt ˈkwɪkli fɔːr taɪm ɪz ˈlɪmɪtɪd/</div>
                        <div class="text-gray-700">我们必须快速行动，因为时间有限。</div>
                    </div>
                    <div class="card bg-neutral-50 border border-neutral-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">演讲表达</div>
                        <div class="keyword text-lg mb-1">We face challenges, but we also see opportunities.</div>
                        <div class="text-sm text-gray-600 mb-1">/wi feɪs ˈtʃælɪndʒɪz bʌt wi ˈɔːlsoʊ siː ˌɑːpərˈtuːnətiz/</div>
                        <div class="text-gray-700">我们面临挑战，但也看到了机遇。</div>
                    </div>
                    <div class="card bg-zinc-50 border border-zinc-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">报告总结</div>
                        <div class="keyword text-lg mb-1">Sales declined, so we need new strategies.</div>
                        <div class="text-sm text-gray-600 mb-1">/seɪlz dɪˈklaɪnd soʊ wi niːd nu ˈstrætədʒiz/</div>
                        <div class="text-gray-700">销售下降了，所以我们需要新策略。</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">学术写作中的连词</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">研究发现</div>
                        <div class="keyword text-lg mb-1">The data shows improvement, yet further research is needed.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈdeɪtə ʃoʊz ɪmˈpruːvmənt jet ˈfɜːrðər rɪˈsɜːrtʃ ɪz ˈniːdɪd/</div>
                        <div class="text-gray-700">数据显示有改善，但还需要进一步研究。</div>
                    </div>
                    <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">论文论证</div>
                        <div class="keyword text-lg mb-1">The theory is sound, for it explains the phenomenon.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈθiːəri ɪz saʊnd fɔːr ɪt ɪkˈspleɪnz ðə fəˈnɑːmənən/</div>
                        <div class="text-gray-700">这个理论是可靠的，因为它解释了现象。</div>
                    </div>
                    <div class="card bg-fuchsia-50 border border-fuchsia-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">实验结果</div>
                        <div class="keyword text-lg mb-1">Method A failed, so we tried Method B.</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈmeθəd eɪ feɪld soʊ wi traɪd ˈmeθəd biː/</div>
                        <div class="text-gray-700">方法A失败了，所以我们尝试了方法B。</div>
                    </div>
                    <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">结论总结</div>
                        <div class="keyword text-lg mb-1">Neither hypothesis was proven, nor was the original theory.</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈniːðər haɪˈpɑːθəsɪs wʌz ˈpruːvən nɔːr wʌz ði əˈrɪdʒənəl ˈθiːəri/</div>
                        <div class="text-gray-700">假设都没有被证实，原始理论也没有。</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 语调和重音指导 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">语调和重音指导</h3>
            <p class="text-gray-700 mb-4 leading-7">正确的语调和重音可以让你的英语听起来更地道，更符合美式英语的表达习惯。</p>
            
            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">连词的重音规律</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">弱读连词</div>
                        <div class="keyword text-lg mb-1">Bread <span style="color: #999;">and</span> butter</div>
                        <div class="text-sm text-gray-600 mb-1">/bred ən ˈbʌtər/</div>
                        <div class="text-gray-700">面包和黄油（and弱读为/ən/）</div>
                    </div>
                    <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">强调对比时重读</div>
                        <div class="keyword text-lg mb-1">Not this <strong>BUT</strong> that</div>
                        <div class="text-sm text-gray-600 mb-1">/nɑːt ðɪs BʌT ðæt/</div>
                        <div class="text-gray-700">不是这个而是那个（but需要重读）</div>
                    </div>
                    <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">选择时重读or</div>
                        <div class="keyword text-lg mb-1">Coffee <strong>OR</strong> tea?</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈkɔːfi ɔːr tiː/</div>
                        <div class="text-gray-700">咖啡还是茶？（疑问时or重读）</div>
                    </div>
                    <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">因果关系重读</div>
                        <div class="keyword text-lg mb-1">I'm tired, <strong>SO</strong> I'm leaving</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪm ˈtaɪərd soʊ aɪm ˈliːvɪŋ/</div>
                        <div class="text-gray-700">我累了，所以要走了（so适度重读）</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">语调模式</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">列举语调（升调）</div>
                        <div class="keyword text-lg mb-1">I need milk↗, eggs↗, and bread↘.</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪ niːd mɪlk egz ænd bred/</div>
                        <div class="text-gray-700">我需要牛奶、鸡蛋和面包。</div>
                    </div>
                    <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">对比语调</div>
                        <div class="keyword text-lg mb-1">He's smart↗, but lazy↘.</div>
                        <div class="text-sm text-gray-600 mb-1">/hiz smɑːrt bʌt ˈleɪzi/</div>
                        <div class="text-gray-700">他聪明，但懒惰。</div>
                    </div>
                    <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">选择疑问语调</div>
                        <div class="keyword text-lg mb-1">Tea↗ or coffee↘?</div>
                        <div class="text-sm text-gray-600 mb-1">/tiː ɔːr ˈkɔːfi/</div>
                        <div class="text-gray-700">茶还是咖啡？</div>
                    </div>
                    <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">因果关系语调</div>
                        <div class="keyword text-lg mb-1">It rained↗, so we stayed home↘.</div>
                        <div class="text-sm text-gray-600 mb-1">/ɪt reɪnd soʊ wi steɪd hoʊm/</div>
                        <div class="text-gray-700">下雨了，所以我们待在家里。</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 更多习惯用语和固定搭配 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">地道表达和习语</h3>
            
            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">And的地道表达</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">nice and + 形容词</div>
                        <div class="keyword text-lg mb-1">The weather is nice and warm.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈweðər ɪz naɪs ænd wɔːrm/</div>
                        <div class="text-gray-700">天气很暖和。</div>
                    </div>
                    <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">tried and true</div>
                        <div class="keyword text-lg mb-1">It's a tried and true method.</div>
                        <div class="text-sm text-gray-600 mb-1">/ɪts ə traɪd ænd truː ˈmeθəd/</div>
                        <div class="text-gray-700">这是经过验证的方法。</div>
                    </div>
                    <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">bits and pieces</div>
                        <div class="keyword text-lg mb-1">I collected bits and pieces of information.</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪ kəˈlektɪd bɪts ænd ˈpiːsɪz əv ˌɪnfərˈmeɪʃən/</div>
                        <div class="text-gray-700">我收集了零碎的信息。</div>
                    </div>
                    <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">far and wide</div>
                        <div class="keyword text-lg mb-1">The news spread far and wide.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə nuːz spred fɑːr ænd waɪd/</div>
                        <div class="text-gray-700">消息传得很远很广。</div>
                    </div>
                    <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">safe and sound</div>
                        <div class="keyword text-lg mb-1">They arrived safe and sound.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðeɪ əˈraɪvd seɪf ænd saʊnd/</div>
                        <div class="text-gray-700">他们安全抵达了。</div>
                    </div>
                    <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">peace and quiet</div>
                        <div class="keyword text-lg mb-1">I need some peace and quiet.</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪ niːd sʌm piːs ænd ˈkwaɪət/</div>
                        <div class="text-gray-700">我需要一些安静。</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">But的地道表达</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">but seriously</div>
                        <div class="keyword text-lg mb-1">That was funny, but seriously, we need to focus.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðæt wʌz ˈfʌni bʌt ˈsɪriəsli wi niːd tu ˈfoʊkəs/</div>
                        <div class="text-gray-700">那很好笑，但说真的，我们需要专注。</div>
                    </div>
                    <div class="card bg-fuchsia-50 border border-fuchsia-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">but then again</div>
                        <div class="keyword text-lg mb-1">It's expensive, but then again, it's high quality.</div>
                        <div class="text-sm text-gray-600 mb-1">/ɪts ɪkˈspensɪv bʌt ðen əˈgen ɪts haɪ ˈkwɑːləti/</div>
                        <div class="text-gray-700">很贵，但话说回来，质量很好。</div>
                    </div>
                    <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">all but</div>
                        <div class="keyword text-lg mb-1">The project is all but finished.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈprɑːdʒekt ɪz ɔːl bʌt ˈfɪnɪʃt/</div>
                        <div class="text-gray-700">项目基本完成了。</div>
                    </div>
                    <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">but for</div>
                        <div class="keyword text-lg mb-1">But for your help, I would have failed.</div>
                        <div class="text-sm text-gray-600 mb-1">/bʌt fɔːr jʊr help aɪ wʊd hæv feɪld/</div>
                        <div class="text-gray-700">要不是你的帮助，我就失败了。</div>
                    </div>
                    <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">can't help but</div>
                        <div class="keyword text-lg mb-1">I can't help but smile.</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪ kænt help bʌt smaɪl/</div>
                        <div class="text-gray-700">我忍不住要笑。</div>
                    </div>
                    <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">but of course</div>
                        <div class="keyword text-lg mb-1">You can borrow my car, but of course!</div>
                        <div class="text-sm text-gray-600 mb-1">/ju kæn ˈbɑːroʊ maɪ kɑːr bʌt əv kɔːrs/</div>
                        <div class="text-gray-700">你可以借我的车，当然可以！</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">So的地道表达</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="card bg-slate-50 border border-slate-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">so far so good</div>
                        <div class="keyword text-lg mb-1">How's the project? So far so good.</div>
                        <div class="text-sm text-gray-600 mb-1">/haʊz ðə ˈprɑːdʒekt soʊ fɑːr soʊ gʊd/</div>
                        <div class="text-gray-700">项目怎么样？到目前为止还不错。</div>
                    </div>
                    <div class="card bg-stone-50 border border-stone-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">so what</div>
                        <div class="keyword text-lg mb-1">I'm late, so what?</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪm leɪt soʊ wʌt/</div>
                        <div class="text-gray-700">我迟到了，那又怎样？</div>
                    </div>
                    <div class="card bg-neutral-50 border border-neutral-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">so to speak</div>
                        <div class="keyword text-lg mb-1">He's the boss, so to speak.</div>
                        <div class="text-sm text-gray-600 mb-1">/hiz ðə bɔːs soʊ tu spiːk/</div>
                        <div class="text-gray-700">可以说他是老板。</div>
                    </div>
                    <div class="card bg-zinc-50 border border-zinc-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">so much for</div>
                        <div class="keyword text-lg mb-1">So much for our vacation plans.</div>
                        <div class="text-sm text-gray-600 mb-1">/soʊ mʌtʃ fɔːr aʊər vəˈkeɪʃən plænz/</div>
                        <div class="text-gray-700">我们的度假计划泡汤了。</div>
                    </div>
                    <div class="card bg-gray-50 border border-gray-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">so long as</div>
                        <div class="keyword text-lg mb-1">You can stay, so long as you're quiet.</div>
                        <div class="text-sm text-gray-600 mb-1">/ju kæn steɪ soʊ lɔːŋ æz jʊr ˈkwaɪət/</div>
                        <div class="text-gray-700">只要你安静，你就可以留下。</div>
                    </div>
                    <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">and so on</div>
                        <div class="keyword text-lg mb-1">Apples, oranges, bananas, and so on.</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈæpəlz ˈɔːrɪndʒɪz bəˈnænəz ænd soʊ ɑːn/</div>
                        <div class="text-gray-700">苹果、橙子、香蕉等等。</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 常见错误和避免方法 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">常见错误和避免方法</h3>
            
            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">标点符号错误</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-red-100 border border-red-300 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-red-600 mb-1">❌ 错误用法</div>
                        <div class="text-lg mb-1 text-red-700">I like pizza and, I like pasta.</div>
                        <div class="text-sm text-gray-600 mb-1">逗号位置错误</div>
                        <div class="text-gray-700">连接短语时不需要逗号</div>
                    </div>
                    <div class="card bg-green-100 border border-green-300 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-green-600 mb-1">✅ 正确用法</div>
                        <div class="keyword text-lg mb-1">I like pizza, and I like pasta.</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪ laɪk ˈpiːtsə ænd aɪ laɪk ˈpɑːstə/</div>
                        <div class="text-gray-700">连接完整句子时需要逗号</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">连词选择错误</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-red-100 border border-red-300 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-red-600 mb-1">❌ 逻辑错误</div>
                        <div class="text-lg mb-1 text-red-700">I was hungry, but I ate lunch.</div>
                        <div class="text-sm text-gray-600 mb-1">逻辑关系不当</div>
                        <div class="text-gray-700">饿了就吃饭是自然的，不是转折</div>
                    </div>
                    <div class="card bg-green-100 border border-green-300 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-green-600 mb-1">✅ 逻辑正确</div>
                        <div class="keyword text-lg mb-1">I was hungry, so I ate lunch.</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪ wʌz ˈhʌŋgri soʊ aɪ eɪt lʌntʃ/</div>
                        <div class="text-gray-700">因果关系清晰</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">重复连词错误</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-red-100 border border-red-300 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-red-600 mb-1">❌ 重复错误</div>
                        <div class="text-lg mb-1 text-red-700">Although it was raining, but we went out.</div>
                        <div class="text-sm text-gray-600 mb-1">although和but重复</div>
                        <div class="text-gray-700">不能同时使用两个转折连词</div>
                    </div>
                    <div class="card bg-green-100 border border-green-300 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-green-600 mb-1">✅ 单一连词</div>
                        <div class="keyword text-lg mb-1">Although it was raining, we went out.</div>
                        <div class="text-sm text-gray-600 mb-1">/ɔːlˈðoʊ ɪt wʌz ˈreɪnɪŋ wi went aʊt/</div>
                        <div class="text-gray-700">只使用一个转折连词</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 实践练习建议 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">学习建议和练习方法</h3>
            
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-4">
                <h4 class="text-lg font-medium mb-3 text-blue-800">日常练习方法</h4>
                <ul class="space-y-2 text-gray-700">
                    <li><span class="keyword">• 听力练习</span>：观看美剧、新闻，注意连词的使用和语调</li>
                    <li><span class="keyword">• 朗读练习</span>：大声朗读包含连词的句子，练习正确的语调和重音</li>
                    <li><span class="keyword">• 造句练习</span>：每天用不同的连词造句，逐渐增加复杂度</li>
                    <li><span class="keyword">• 写作练习</span>：在写作中有意识地使用各种连词，让文章更流畅</li>
                </ul>
            </div>

            <div class="bg-green-50 border border-green-200 rounded-lg p-6 mb-4">
                <h4 class="text-lg font-medium mb-3 text-green-800">记忆技巧</h4>
                <ul class="space-y-2 text-gray-700">
                    <li><span class="keyword">• FANBOYS口诀</span>：For, And, Nor, But, Or, Yet, So</li>
                    <li><span class="keyword">• 语境记忆</span>：将连词与具体场景结合记忆</li>
                    <li><span class="keyword">• 对比学习</span>：比较相似连词的区别和用法</li>
                    <li><span class="keyword">• 习语积累</span>：收集包含连词的常用表达</li>
                </ul>
            </div>

            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                <h4 class="text-lg font-medium mb-3 text-yellow-800">进阶学习目标</h4>
                <ul class="space-y-2 text-gray-700">
                    <li><span class="keyword">• 自然流畅</span>：能够在对话中自然使用各种连词</li>
                    <li><span class="keyword">• 语调准确</span>：掌握连词的正确重音和语调模式</li>
                    <li><span class="keyword">• 风格适配</span>：能根据正式程度选择合适的连词</li>
                    <li><span class="keyword">• 复合运用</span>：灵活组合多个连词表达复杂意思</li>
                </ul>
            </div>
        </section>

        <!-- 总结 -->
        <section class="mb-6">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">学习要点总结</h3>
            <div class="bg-gray-50 border border-gray-200 rounded-lg p-6">
                <ul class="space-y-3 text-gray-700">
                    <li><span class="keyword">And</span> - 表示并列和递增，是最常用的连词</li>
                    <li><span class="keyword">But</span> - 表示转折对比，用于相反或对立的情况</li>
                    <li><span class="keyword">Or</span> - 表示选择，可以是"或者"也可以是"否则"</li>
                    <li><span class="keyword">So</span> - 表示因果关系的结果，常用于口语</li>
                    <li><span class="keyword">Yet</span> - 表示意外转折，比but更正式</li>
                    <li><span class="keyword">For</span> - 表示原因解释，多用于书面语</li>
                    <li><span class="keyword">Nor</span> - 表示递进否定，通常与neither搭配</li>
                </ul>
            </div>
        </section>

    </div>
</body>
</html>