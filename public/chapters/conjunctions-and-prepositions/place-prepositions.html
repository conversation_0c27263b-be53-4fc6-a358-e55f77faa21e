<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>地点介词学习</title>
    <script src="/static/tailwindcss-3.4.17.js"></script>
    <style>
        .keyword { color: #3d74ed; font-weight: bold; }
        .card { transition: transform 0.1s ease; }
    </style>
</head>
<body class="bg-white p-6">

    <!-- 地点介词概述 -->
    <div class="mb-8">
        <h2 class="text-2xl font-bold mb-4 text-gray-800">地点介词详解</h2>
        <p class="text-gray-700 leading-relaxed mb-4">
            地点介词是用来描述位置关系的重要语法工具。它们帮助我们准确表达物体、人或事物之间的空间关系。
            掌握这些介词对于准确表达位置信息至关重要。
        </p>
    </div>

    <!-- AT 介词详解 -->
    <div class="mb-8">
        <h3 class="text-xl font-semibold mb-4 text-gray-800">AT - 在特定地点</h3>
        <p class="text-gray-700 mb-4">
            <span class="keyword">AT</span> 用于指定确切的地点、位置或活动场所。通常用于较小的、具体的地点。
        </p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
            <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在学校</div>
                <div class="keyword text-lg mb-1">I'm at school.</div>
                <div class="text-sm text-gray-600 mb-1">/aɪm æt skuːl/</div>
                <div class="text-gray-700">我在学校。</div>
            </div>
            
            <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在家</div>
                <div class="keyword text-lg mb-1">She's at home.</div>
                <div class="text-sm text-gray-600 mb-1">/ʃiːz æt hoʊm/</div>
                <div class="text-gray-700">她在家。</div>
            </div>
            
            <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在工作</div>
                <div class="keyword text-lg mb-1">He's at work.</div>
                <div class="text-sm text-gray-600 mb-1">/hiːz æt wɜrk/</div>
                <div class="text-gray-700">他在工作。</div>
            </div>
            
            <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在聚会上</div>
                <div class="keyword text-lg mb-1">We met at the party.</div>
                <div class="text-sm text-gray-600 mb-1">/wi mɛt æt ðə ˈpɑrti/</div>
                <div class="text-gray-700">我们在聚会上见面。</div>
            </div>
            
            <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在地址</div>
                <div class="keyword text-lg mb-1">I live at 123 Main Street.</div>
                <div class="text-sm text-gray-600 mb-1">/aɪ lɪv æt wʌn tuː θri meɪn strit/</div>
                <div class="text-gray-700">我住在主街123号。</div>
            </div>
        </div>
    </div>

    <!-- IN 介词详解 -->
    <div class="mb-8">
        <h3 class="text-xl font-semibold mb-4 text-gray-800">IN - 在内部/范围内</h3>
        <p class="text-gray-700 mb-4">
            <span class="keyword">IN</span> 表示在某个封闭的空间内部，或在某个区域、范围之内。
        </p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
            <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在房间里</div>
                <div class="keyword text-lg mb-1">The cat is in the room.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə kæt ɪz ɪn ðə rum/</div>
                <div class="text-gray-700">猫在房间里。</div>
            </div>
            
            <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在城市里</div>
                <div class="keyword text-lg mb-1">I live in New York.</div>
                <div class="text-sm text-gray-600 mb-1">/aɪ lɪv ɪn nu jɔrk/</div>
                <div class="text-gray-700">我住在纽约。</div>
            </div>
            
            <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在盒子里</div>
                <div class="keyword text-lg mb-1">The toy is in the box.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə tɔɪ ɪz ɪn ðə bɑks/</div>
                <div class="text-gray-700">玩具在盒子里。</div>
            </div>
            
            <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在车里</div>
                <div class="keyword text-lg mb-1">She's sitting in the car.</div>
                <div class="text-sm text-gray-600 mb-1">/ʃiz ˈsɪtɪŋ ɪn ðə kɑr/</div>
                <div class="text-gray-700">她坐在车里。</div>
            </div>
            
            <div class="card bg-gray-50 border border-gray-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在国家里</div>
                <div class="keyword text-lg mb-1">I was born in China.</div>
                <div class="text-sm text-gray-600 mb-1">/aɪ wʌz bɔrn ɪn ˈtʃaɪnə/</div>
                <div class="text-gray-700">我出生在中国。</div>
            </div>
        </div>
    </div>

    <!-- ON 介词详解 -->
    <div class="mb-8">
        <h3 class="text-xl font-semibold mb-4 text-gray-800">ON - 在表面上</h3>
        <p class="text-gray-700 mb-4">
            <span class="keyword">ON</span> 表示在某个表面上，或与某个表面接触。
        </p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
            <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在桌子上</div>
                <div class="keyword text-lg mb-1">The book is on the table.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə bʊk ɪz ɑn ðə ˈteɪbəl/</div>
                <div class="text-gray-700">书在桌子上。</div>
            </div>
            
            <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在墙上</div>
                <div class="keyword text-lg mb-1">The picture is on the wall.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə ˈpɪktʃər ɪz ɑn ðə wɔl/</div>
                <div class="text-gray-700">画在墙上。</div>
            </div>
            
            <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在地板上</div>
                <div class="keyword text-lg mb-1">The carpet is on the floor.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə ˈkɑrpɪt ɪz ɑn ðə flɔr/</div>
                <div class="text-gray-700">地毯在地板上。</div>
            </div>
            
            <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在街道上</div>
                <div class="keyword text-lg mb-1">I live on Oak Street.</div>
                <div class="text-sm text-gray-600 mb-1">/aɪ lɪv ɑn oʊk strit/</div>
                <div class="text-gray-700">我住在橡树街上。</div>
            </div>
        </div>
    </div>

    <!-- UNDER 介词详解 -->
    <div class="mb-8">
        <h3 class="text-xl font-semibold mb-4 text-gray-800">UNDER - 在下面</h3>
        <p class="text-gray-700 mb-4">
            <span class="keyword">UNDER</span> 表示在某物的下方，通常有被覆盖或保护的含义。
        </p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
            <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在桌子下</div>
                <div class="keyword text-lg mb-1">The cat is under the table.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə kæt ɪz ˈʌndər ðə ˈteɪbəl/</div>
                <div class="text-gray-700">猫在桌子下面。</div>
            </div>
            
            <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在桥下</div>
                <div class="keyword text-lg mb-1">The boat passed under the bridge.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə boʊt pæst ˈʌndər ðə brɪdʒ/</div>
                <div class="text-gray-700">船从桥下经过。</div>
            </div>
            
            <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在床下</div>
                <div class="keyword text-lg mb-1">My shoes are under the bed.</div>
                <div class="text-sm text-gray-600 mb-1">/maɪ ʃuz ɑr ˈʌndər ðə bɛd/</div>
                <div class="text-gray-700">我的鞋子在床下。</div>
            </div>
            
            <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在雨伞下</div>
                <div class="keyword text-lg mb-1">We stood under the umbrella.</div>
                <div class="text-sm text-gray-600 mb-1">/wi stʊd ˈʌndər ði ʌmˈbrɛlə/</div>
                <div class="text-gray-700">我们站在雨伞下。</div>
            </div>
        </div>
    </div>

    <!-- OVER 介词详解 -->
    <div class="mb-8">
        <h3 class="text-xl font-semibold mb-4 text-gray-800">OVER - 在上方/越过</h3>
        <p class="text-gray-700 mb-4">
            <span class="keyword">OVER</span> 表示在某物的上方，或从一边到另一边的动作。
        </p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
            <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">飞过</div>
                <div class="keyword text-lg mb-1">The plane flew over the city.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə pleɪn flu ˈoʊvər ðə ˈsɪti/</div>
                <div class="text-gray-700">飞机飞过城市上空。</div>
            </div>
            
            <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在头顶</div>
                <div class="keyword text-lg mb-1">The lamp hangs over the table.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə læmp hæŋz ˈoʊvər ðə ˈteɪbəl/</div>
                <div class="text-gray-700">灯悬挂在桌子上方。</div>
            </div>
            
            <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">跳过</div>
                <div class="keyword text-lg mb-1">The horse jumped over the fence.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə hɔrs dʒʌmpt ˈoʊvər ðə fɛns/</div>
                <div class="text-gray-700">马跳过了围栏。</div>
            </div>
            
            <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">遍布</div>
                <div class="keyword text-lg mb-1">Snow fell all over the ground.</div>
                <div class="text-sm text-gray-600 mb-1">/snoʊ fɛl ɔl ˈoʊvər ðə graʊnd/</div>
                <div class="text-gray-700">雪花飘洒在地面上。</div>
            </div>
        </div>
    </div>

    <!-- BETWEEN 介词详解 -->
    <div class="mb-8">
        <h3 class="text-xl font-semibold mb-4 text-gray-800">BETWEEN - 在两者之间</h3>
        <p class="text-gray-700 mb-4">
            <span class="keyword">BETWEEN</span> 表示在两个人或物体之间的位置。
        </p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
            <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">两个人之间</div>
                <div class="keyword text-lg mb-1">I sat between Tom and Jerry.</div>
                <div class="text-sm text-gray-600 mb-1">/aɪ sæt bɪˈtwin tɑm ænd ˈdʒɛri/</div>
                <div class="text-gray-700">我坐在汤姆和杰瑞之间。</div>
            </div>
            
            <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">两个城市之间</div>
                <div class="keyword text-lg mb-1">The road runs between two cities.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə roʊd rʌnz bɪˈtwin tu ˈsɪtiz/</div>
                <div class="text-gray-700">这条路连接两个城市。</div>
            </div>
            
            <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">两棵树之间</div>
                <div class="keyword text-lg mb-1">The house stands between two trees.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə haʊs stændz bɪˈtwin tu triz/</div>
                <div class="text-gray-700">房子坐落在两棵树之间。</div>
            </div>
        </div>
    </div>

    <!-- AMONG 介词详解 -->
    <div class="mb-8">
        <h3 class="text-xl font-semibold mb-4 text-gray-800">AMONG - 在多者之间</h3>
        <p class="text-gray-700 mb-4">
            <span class="keyword">AMONG</span> 表示在三个或更多的人或物体之间。
        </p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
            <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在朋友中</div>
                <div class="keyword text-lg mb-1">She is popular among her friends.</div>
                <div class="text-sm text-gray-600 mb-1">/ʃi ɪz ˈpɑpjələr əˈmʌŋ hər frɛndz/</div>
                <div class="text-gray-700">她在朋友中很受欢迎。</div>
            </div>
            
            <div class="card bg-gray-50 border border-gray-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在花丛中</div>
                <div class="keyword text-lg mb-1">The bee flies among the flowers.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə bi flaɪz əˈmʌŋ ðə ˈflaʊərz/</div>
                <div class="text-gray-700">蜜蜂在花丛中飞舞。</div>
            </div>
            
            <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在学生中</div>
                <div class="keyword text-lg mb-1">He is the tallest among all students.</div>
                <div class="text-sm text-gray-600 mb-1">/hi ɪz ðə ˈtɔləst əˈmʌŋ ɔl ˈstudənts/</div>
                <div class="text-gray-700">他是所有学生中最高的。</div>
            </div>
        </div>
    </div>

    <!-- BESIDE 介词详解 -->
    <div class="mb-8">
        <h3 class="text-xl font-semibold mb-4 text-gray-800">BESIDE - 在旁边</h3>
        <p class="text-gray-700 mb-4">
            <span class="keyword">BESIDE</span> 表示在某物的旁边，紧挨着。
        </p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
            <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在河边</div>
                <div class="keyword text-lg mb-1">The house is beside the river.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə haʊs ɪz bɪˈsaɪd ðə ˈrɪvər/</div>
                <div class="text-gray-700">房子在河边。</div>
            </div>
            
            <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">坐在旁边</div>
                <div class="keyword text-lg mb-1">She sits beside me.</div>
                <div class="text-sm text-gray-600 mb-1">/ʃi sɪts bɪˈsaɪd mi/</div>
                <div class="text-gray-700">她坐在我旁边。</div>
            </div>
            
            <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在床边</div>
                <div class="keyword text-lg mb-1">The lamp is beside the bed.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə læmp ɪz bɪˈsaɪd ðə bɛd/</div>
                <div class="text-gray-700">台灯在床边。</div>
            </div>
        </div>
    </div>

    <!-- BEHIND 介词详解 -->
    <div class="mb-8">
        <h3 class="text-xl font-semibold mb-4 text-gray-800">BEHIND - 在后面</h3>
        <p class="text-gray-700 mb-4">
            <span class="keyword">BEHIND</span> 表示在某物的后面或背后。
        </p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
            <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在树后</div>
                <div class="keyword text-lg mb-1">The dog is hiding behind the tree.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə dɔg ɪz ˈhaɪdɪŋ bɪˈhaɪnd ðə tri/</div>
                <div class="text-gray-700">狗躲在树后面。</div>
            </div>
            
            <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在房子后</div>
                <div class="keyword text-lg mb-1">The garden is behind the house.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə ˈgɑrdən ɪz bɪˈhaɪnd ðə haʊs/</div>
                <div class="text-gray-700">花园在房子后面。</div>
            </div>
            
            <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">跟在后面</div>
                <div class="keyword text-lg mb-1">The children walked behind their teacher.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə ˈtʃɪldrən wɔkt bɪˈhaɪnd ðɛr ˈtitʃər/</div>
                <div class="text-gray-700">孩子们跟在老师后面走。</div>
            </div>
        </div>
    </div>

    <!-- IN FRONT OF 介词详解 -->
    <div class="mb-8">
        <h3 class="text-xl font-semibold mb-4 text-gray-800">IN FRONT OF - 在前面</h3>
        <p class="text-gray-700 mb-4">
            <span class="keyword">IN FRONT OF</span> 表示在某物的前面或前方。
        </p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
            <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在学校前</div>
                <div class="keyword text-lg mb-1">The bus stops in front of the school.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə bʌs stɑps ɪn frʌnt ʌv ðə skul/</div>
                <div class="text-gray-700">公交车停在学校前面。</div>
            </div>
            
            <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">站在前面</div>
                <div class="keyword text-lg mb-1">She stands in front of the mirror.</div>
                <div class="text-sm text-gray-600 mb-1">/ʃi stændz ɪn frʌnt ʌv ðə ˈmɪrər/</div>
                <div class="text-gray-700">她站在镜子前面。</div>
            </div>
            
            <div class="card bg-gray-50 border border-gray-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在电视前</div>
                <div class="keyword text-lg mb-1">The sofa is in front of the TV.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə ˈsoʊfə ɪz ɪn frʌnt ʌv ðə ˌtiˈvi/</div>
                <div class="text-gray-700">沙发在电视前面。</div>
            </div>
        </div>
    </div>

    <!-- NEAR 介词详解 -->
    <div class="mb-8">
        <h3 class="text-xl font-semibold mb-4 text-gray-800">NEAR - 在附近</h3>
        <p class="text-gray-700 mb-4">
            <span class="keyword">NEAR</span> 表示在某物的附近，距离不远。
        </p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
            <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在银行附近</div>
                <div class="keyword text-lg mb-1">The restaurant is near the bank.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə ˈrɛstərɑnt ɪz nɪr ðə bæŋk/</div>
                <div class="text-gray-700">餐厅在银行附近。</div>
            </div>
            
            <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">住在附近</div>
                <div class="keyword text-lg mb-1">I live near the park.</div>
                <div class="text-sm text-gray-600 mb-1">/aɪ lɪv nɪr ðə pɑrk/</div>
                <div class="text-gray-700">我住在公园附近。</div>
            </div>
            
            <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">靠近海边</div>
                <div class="keyword text-lg mb-1">The hotel is near the beach.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə hoʊˈtɛl ɪz nɪr ðə bitʃ/</div>
                <div class="text-gray-700">酒店在海滩附近。</div>
            </div>
        </div>
    </div>

    <!-- ABOVE 介词详解 -->
    <div class="mb-8">
        <h3 class="text-xl font-semibold mb-4 text-gray-800">ABOVE - 在上方</h3>
        <p class="text-gray-700 mb-4">
            <span class="keyword">ABOVE</span> 表示在某物的上方，通常指高度上的位置关系。
        </p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
            <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在云层上</div>
                <div class="keyword text-lg mb-1">The plane flies above the clouds.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə pleɪn flaɪz əˈbʌv ðə klaʊdz/</div>
                <div class="text-gray-700">飞机在云层上方飞行。</div>
            </div>
            
            <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">挂在上方</div>
                <div class="keyword text-lg mb-1">The clock hangs above the door.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə klɑk hæŋz əˈbʌv ðə dɔr/</div>
                <div class="text-gray-700">时钟挂在门的上方。</div>
            </div>
            
            <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">高于海平面</div>
                <div class="keyword text-lg mb-1">The mountain is above sea level.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə ˈmaʊntən ɪz əˈbʌv si ˈlɛvəl/</div>
                <div class="text-gray-700">山在海平面以上。</div>
            </div>
        </div>
    </div>

    <!-- BELOW 介词详解 -->
    <div class="mb-8">
        <h3 class="text-xl font-semibold mb-4 text-gray-800">BELOW - 在下方</h3>
        <p class="text-gray-700 mb-4">
            <span class="keyword">BELOW</span> 表示在某物的下方，通常指高度上的位置关系。
        </p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
            <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在海平面下</div>
                <div class="keyword text-lg mb-1">The submarine is below sea level.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə ˈsʌbmərin ɪz bɪˈloʊ si ˈlɛvəl/</div>
                <div class="text-gray-700">潜水艇在海平面以下。</div>
            </div>
            
            <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在山脚下</div>
                <div class="keyword text-lg mb-1">The village is below the mountain.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə ˈvɪlɪdʒ ɪz bɪˈloʊ ðə ˈmaʊntən/</div>
                <div class="text-gray-700">村庄在山脚下。</div>
            </div>
            
            <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在零度以下</div>
                <div class="keyword text-lg mb-1">The temperature is below zero.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə ˈtɛmprətʃər ɪz bɪˈloʊ ˈzɪroʊ/</div>
                <div class="text-gray-700">温度在零度以下。</div>
            </div>
        </div>
    </div>

    <!-- ACROSS 介词详解 -->
    <div class="mb-8">
        <h3 class="text-xl font-semibold mb-4 text-gray-800">ACROSS - 穿过/对面</h3>
        <p class="text-gray-700 mb-4">
            <span class="keyword">ACROSS</span> 表示从一边到另一边，或在某物的对面。
        </p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
            <div class="card bg-gray-50 border border-gray-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">穿过马路</div>
                <div class="keyword text-lg mb-1">She walked across the street.</div>
                <div class="text-sm text-gray-600 mb-1">/ʃi wɔkt əˈkrɔs ðə strit/</div>
                <div class="text-gray-700">她穿过了马路。</div>
            </div>
            
            <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在对面</div>
                <div class="keyword text-lg mb-1">The bank is across from the school.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə bæŋk ɪz əˈkrɔs frʌm ðə skul/</div>
                <div class="text-gray-700">银行在学校对面。</div>
            </div>
            
            <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">游过河</div>
                <div class="keyword text-lg mb-1">The fish swam across the river.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə fɪʃ swæm əˈkrɔs ðər ˈrɪvər/</div>
                <div class="text-gray-700">鱼游过了河。</div>
            </div>
        </div>
    </div>

    <!-- 总结 -->
    <div class="mb-8">
        <h3 class="text-xl font-semibold mb-4 text-gray-800">地点介词使用要点</h3>
        <div class="bg-gray-50 border border-gray-200 rounded-lg p-6">
            <ul class="space-y-3 text-gray-700">
                <li><span class="keyword">AT</span> - 用于具体地点：at school, at home, at the party</li>
                <li><span class="keyword">IN</span> - 用于封闭空间：in the room, in the car, in New York</li>
                <li><span class="keyword">ON</span> - 用于表面：on the table, on the wall, on the street</li>
                <li><span class="keyword">UNDER/OVER</span> - 表示上下位置关系</li>
                <li><span class="keyword">BETWEEN/AMONG</span> - BETWEEN用于两者间，AMONG用于多者间</li>
                <li><span class="keyword">BESIDE/NEAR</span> - 表示旁边和附近的位置</li>
                <li><span class="keyword">ABOVE/BELOW</span> - 表示高度上的位置关系</li>
                <li><span class="keyword">ACROSS</span> - 表示穿过或对面的位置</li>
            </ul>
        </div>
    </div>

</body>
</html>